<?php

namespace App\Modules\PendingDelete\Services;

use App\Modules\PendingDelete\Jobs\DomainEppDeletion;
use App\Modules\PendingDelete\Jobs\SendDomainRedemptionNotification;
use App\Util\Constant\UserDomainStatus;
use App\Util\Constant\DomainStatus;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use App\Modules\AdminHistory\Constants\HistoryType;
use App\Events\AdminActionEvent;
use Carbon\Carbon;
use stdClass;

class PendingDeleteService
{
    private Carbon $now;
    private $isJob = true;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        $pendingDeleteService = new self;

        return $pendingDeleteService;
    }

    public function delete(array $ids): void
    {
        $pendingDomains = DatabaseQueryService::instance()->pendingDomains($ids);
        
        $this->localDelete($pendingDomains);
        $this->processEppDeletion($pendingDomains, $ids);
        $this->notify($ids);
    }

    // PRIVATE Function

    private function localDelete(Builder $pendingDomains): void
    {
        $registeredDomainsIds = $pendingDomains->pluck('registered_domain_id')->all();

        $domainIds = DB::client()->table('registered_domains')
            ->whereIn('id', $registeredDomainsIds)
            ->pluck('domain_id')
            ->all();

        DB::client()->table('registered_domains')->whereIn('id', $registeredDomainsIds)->update([
            'status' => UserDomainStatus::DELETED,
            'deleted_at' => $this->now,
            'updated_at' => $this->now
        ]);

        DB::client()->table('domains')->whereIn('id', $domainIds)->update([
            'status' => DomainStatus::REDEMPTION,
            'updated_at' => $this->now
        ]);

        $pendingDomains->update([
            'deleted_at' => $this->now,
            'updated_at' => $this->now,
            'deleted_by' => auth()->user() ? auth()->user()->email : 'System'
        ]);
    }

    private function processEppDeletion(Builder $pendingDomains): void
    {
        $domains = DatabaseQueryService::instance()->getDomainRecords($pendingDomains);
        $domainNames = collect($domains)->pluck('name')->all();

        $domainCount = count($domainNames);
        $domainText = $domainCount === 1 ? 'Domain' : 'Domains';
        $domainList = implode(', ', $domainNames);
        $adminEmail = auth()->user()->email;

        event(new AdminActionEvent( auth()->user()->id ?? null, HistoryType::DOMAIN_DELETED, "{$domainText} successfully deleted: {$domainList} by {$adminEmail})" ));

        foreach ($domains as $domain) {
            if ($this->isJob) {
                $this->jobDispatch($domain);
                continue;
            }

            $this->whenJobIsDisabled($domain);
        }
    }

    private function jobDispatch(stdClass $domain): void
    {
        DomainEppDeletion::dispatch(
            $domain->id,
            $domain->domain_id,
            $domain->name,
            $domain->registered_domain_id,
            $domain->user_id,
            $domain->user_email,
        );
    }

    private function whenJobIsDisabled(stdClass $domain): void
    {
        DomainEppDeleteJobService::instance()->eppDelete([
            'deleteId' => $domain->id,
            'domainId' => $domain->domain_id,
            'domainName' => $domain->name,
            'registeredDomainId' => $domain->registered_domain_id,
            'userId' => $domain->user_id,
            'email' => $domain->user_email
        ]);
    }
    private function notify(array $ids): void
    {
        $pendingDomains = DatabaseQueryService::instance()->pendingDomains($ids);
        $domains = DatabaseQueryService::instance()->getDomainRecords($pendingDomains);

        $domainsByEmail = [];
        foreach ($domains as $domain) {
            $email = $domain->user_email;
            if (!isset($domainsByEmail[$email])) {
                $domainsByEmail[$email] = [];
            }
            $domainsByEmail[$email][] = $domain;
        }

        if (!empty($domainsByEmail)) {
            SendDomainRedemptionNotification::dispatch($domainsByEmail);
        }
    }

}
