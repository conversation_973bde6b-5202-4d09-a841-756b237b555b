[2025-08-13 01:22:18] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 1 days  
[2025-08-13 01:22:18] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:26:27] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 30 days  
[2025-08-13 01:26:27] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:27:39] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 30 days  
[2025-08-13 01:27:39] local.INFO: DomainRedemptionScheduler: Found 2 total domains in redemption status  
[2025-08-13 01:27:39] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."deleted_at", "domains"."status" as "domain_status", "registered_domains"."id" as "registered_domain_id", "users"."id" as "user_id", "users"."email" as "user_email" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" inner join "user_contacts" on "user_contacts"."id" = "registered_domains"."user_contact_registrar_id" inner join "users" on "users"."id" = "user_contacts"."user_id" where "domains"."status" = ? and "domains"."deleted_at" <= ? and "domains"."deleted_at" is not null and "pending_domain_deletions"."deleted_at" is null  
[2025-08-13 01:27:39] local.INFO: RedemptionPeriodQuery Bindings: ["REDEMPTION","2025-07-14T01:27:39.764087Z"]  
[2025-08-13 01:27:39] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:33:38] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 30 days  
[2025-08-13 01:33:39] local.INFO: DomainRedemptionScheduler: Found 2 total domains in redemption status  
[2025-08-13 01:33:39] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry", "domains"."status" as "domain_status", "registered_domains"."id" as "registered_domain_id", "users"."id" as "user_id", "users"."email" as "user_email" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" inner join "user_contacts" on "user_contacts"."id" = "registered_domains"."user_contact_registrar_id" inner join "users" on "users"."id" = "user_contacts"."user_id" where "domains"."status" = ? and "domains"."expiry" <= ? and "domains"."expiry" is not null and "pending_domain_deletions"."deleted_at" is null  
[2025-08-13 01:33:39] local.INFO: RedemptionPeriodQuery Bindings: ["REDEMPTION",1752456819146]  
[2025-08-13 01:33:39] local.INFO: DomainRedemptionScheduler: Found 1 domains for deletion: cascadecloud.com  
[2025-08-13 01:33:39] local.ERROR: DomainRedemptionScheduler: Attempt to read property "email" on null  
[2025-08-13 01:33:39] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"DomainRedemptionScheduler: Attempt to read property \"email\" on null","code":0}  
[2025-08-13 01:34:59] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 30 days  
[2025-08-13 01:34:59] local.INFO: DomainRedemptionScheduler: Found 2 total domains in redemption status  
[2025-08-13 01:34:59] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry", "domains"."status" as "domain_status", "registered_domains"."id" as "registered_domain_id", "users"."id" as "user_id", "users"."email" as "user_email" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" inner join "user_contacts" on "user_contacts"."id" = "registered_domains"."user_contact_registrar_id" inner join "users" on "users"."id" = "user_contacts"."user_id" where "domains"."status" = ? and "domains"."expiry" <= ? and "domains"."expiry" is not null and "pending_domain_deletions"."deleted_at" is null  
[2025-08-13 01:34:59] local.INFO: RedemptionPeriodQuery Bindings: ["REDEMPTION",1752456899295]  
[2025-08-13 01:34:59] local.INFO: DomainRedemptionScheduler: Found 1 domains for deletion: cascadecloud.com  
[2025-08-13 01:34:59] local.ERROR: DomainRedemptionScheduler: Attempt to read property "email" on null  
[2025-08-13 01:34:59] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"DomainRedemptionScheduler: Attempt to read property \"email\" on null","code":0}  
[2025-08-13 01:36:46] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 30 days  
[2025-08-13 01:36:46] local.INFO: DomainRedemptionScheduler: Found 2 total domains in redemption status  
[2025-08-13 01:36:46] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry", "domains"."status" as "domain_status", "registered_domains"."id" as "registered_domain_id", "users"."id" as "user_id", "users"."email" as "user_email" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" inner join "user_contacts" on "user_contacts"."id" = "registered_domains"."user_contact_registrar_id" inner join "users" on "users"."id" = "user_contacts"."user_id" where "domains"."status" = ? and "domains"."expiry" <= ? and "domains"."expiry" is not null and "pending_domain_deletions"."deleted_at" is null  
[2025-08-13 01:36:46] local.INFO: RedemptionPeriodQuery Bindings: ["REDEMPTION",1752457006141]  
[2025-08-13 01:36:46] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:37:26] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 60 days  
[2025-08-13 01:37:26] local.INFO: DomainRedemptionScheduler: Found 2 total domains in redemption status  
[2025-08-13 01:37:26] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry", "domains"."status" as "domain_status", "registered_domains"."id" as "registered_domain_id", "users"."id" as "user_id", "users"."email" as "user_email" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" inner join "user_contacts" on "user_contacts"."id" = "registered_domains"."user_contact_registrar_id" inner join "users" on "users"."id" = "user_contacts"."user_id" where "domains"."status" = ? and "domains"."expiry" <= ? and "domains"."expiry" is not null and "pending_domain_deletions"."deleted_at" is null  
[2025-08-13 01:37:26] local.INFO: RedemptionPeriodQuery Bindings: ["REDEMPTION",1749865046960]  
[2025-08-13 01:37:26] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:40:07] local.INFO: DomainRedemptionScheduler: Starting with redemption period of 70 days  
[2025-08-13 01:40:07] local.INFO: DomainRedemptionScheduler: Found 0 total domains in redemption status  
[2025-08-13 01:40:07] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry", "domains"."status" as "domain_status", "registered_domains"."id" as "registered_domain_id", "users"."id" as "user_id", "users"."email" as "user_email" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" inner join "user_contacts" on "user_contacts"."id" = "registered_domains"."user_contact_registrar_id" inner join "users" on "users"."id" = "user_contacts"."user_id" where "domains"."status" = ? and "domains"."expiry" <= ? and "domains"."expiry" is not null and "pending_domain_deletions"."deleted_at" is null  
[2025-08-13 01:40:07] local.INFO: RedemptionPeriodQuery Bindings: ["REDEMPTION",1749001207151]  
[2025-08-13 01:40:07] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:51:00] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:51:00] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" where "pending_domain_deletions"."deleted_at" is null and "domains"."expiry" <= ? and "domains"."expiry" is not null  
[2025-08-13 01:51:00] local.INFO: RedemptionPeriodQuery Bindings: [1752371460855]  
[2025-08-13 01:51:01] local.INFO: DomainRedemptionScheduler: Found 2 domains for deletion: cosmicnova.net, nexusnova.net  
[2025-08-13 01:51:02] local.INFO: DomainRedemptionScheduler: Successfully processed 2 domains for deletion  
[2025-08-13 01:51:02] local.INFO: DomainRedemptionScheduler: Completed successfully  
[2025-08-13 01:51:36] local.INFO: DomainRedemptionScheduler: Starting with 30 days expired threshold  
[2025-08-13 01:51:36] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" where "pending_domain_deletions"."deleted_at" is null and "domains"."expiry" <= ? and "domains"."expiry" is not null  
[2025-08-13 01:51:36] local.INFO: RedemptionPeriodQuery Bindings: [1752457896948]  
[2025-08-13 01:51:36] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:52:00] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:52:00] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" where "pending_domain_deletions"."deleted_at" is null and "domains"."expiry" <= ? and "domains"."expiry" is not null  
[2025-08-13 01:52:00] local.INFO: RedemptionPeriodQuery Bindings: [1752371520835]  
[2025-08-13 01:52:00] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:52:52] local.INFO: DomainRedemptionScheduler: Starting with 60 days expired threshold  
[2025-08-13 01:52:52] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" where "pending_domain_deletions"."deleted_at" is null and "domains"."expiry" <= ? and "domains"."expiry" is not null  
[2025-08-13 01:52:52] local.INFO: RedemptionPeriodQuery Bindings: [1749865972618]  
[2025-08-13 01:52:52] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:53:00] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:53:00] local.INFO: RedemptionPeriodQuery SQL: select "pending_domain_deletions"."id", "domains"."name", "domains"."expiry" from "pending_domain_deletions" inner join "registered_domains" on "registered_domains"."id" = "pending_domain_deletions"."registered_domain_id" inner join "domains" on "domains"."id" = "registered_domains"."domain_id" where "pending_domain_deletions"."deleted_at" is null and "domains"."expiry" <= ? and "domains"."expiry" is not null  
[2025-08-13 01:53:00] local.INFO: RedemptionPeriodQuery Bindings: [1752371580913]  
[2025-08-13 01:53:00] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:54:01] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:54:01] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:55:01] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:55:01] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:56:00] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:56:00] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:57:00] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:57:00] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:58:00] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:58:00] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 01:59:00] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 01:59:00] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 02:00:00] local.INFO: Deleted 3 expired admin invitation entries.  
[2025-08-13 02:00:01] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 02:00:01] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
[2025-08-13 02:01:01] local.INFO: DomainRedemptionScheduler: Starting with 31 days expired threshold  
[2025-08-13 02:01:01] local.INFO: DomainRedemptionScheduler: No domains found for deletion  
