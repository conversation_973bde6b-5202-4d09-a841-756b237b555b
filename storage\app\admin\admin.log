[2025-07-23 02:33:43] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:33:47] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Validation\\ValidationException","message":"The id field is required.","code":0}  
[2025-07-23 02:36:30] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:36:36] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:36:39] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:39:40] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:39:57] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:39:59] local.INFO: ClientController show method called with ID: 1  
[2025-07-23 02:39:59] local.ERROR: Error in ClientController show: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "name" does not exist
LINE 1: select "id", "name", "email", "created_at" from "users" wher...
                     ^ (Connection: client, SQL: select "id", "name", "email", "created_at" from "users" where "id" = 1 and "deleted_at" is null limit 1)  
[2025-07-23 02:42:09] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:42:12] local.INFO: ClientController show method called with ID: 1  
[2025-07-23 02:50:07] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"user_balances\" does not exist
LINE 1: select * from \"user_balances\" where \"user_id\" = $1 limit 1
                      ^ (Connection: client, SQL: select * from \"user_balances\" where \"user_id\" = 1 limit 1)","code":"42P01"}  
[2025-07-23 02:50:08] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:52:56] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"user_balances\" does not exist
LINE 1: select * from \"user_balances\" where \"user_id\" = $1 limit 1
                      ^ (Connection: client, SQL: select * from \"user_balances\" where \"user_id\" = 1 limit 1)","code":"42P01"}  
[2025-07-23 02:52:57] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 02:52:57] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-07-23 03:03:54] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"user_balances\" does not exist
LINE 1: select * from \"user_balances\" where \"user_id\" = $1 limit 1
                      ^ (Connection: client, SQL: select * from \"user_balances\" where \"user_id\" = 1 limit 1)","code":"42P01"}  
[2025-07-23 03:03:55] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:05:47] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:05:55] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:05:59] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined property: stdClass::$balance","code":0}  
[2025-07-23 03:08:20] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:12:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:13:50] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:13:53] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:14:58] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-23 03:18:12] local.ERROR: {"query":{"date":[["today"]],"email":"<EMAIL>"},"parameter":{"date":[["today"]],"email":"<EMAIL>"},"error":"TypeError","message":"App\\Modules\\Client\\Services\\SecurityLogService::applyDateFilter(): Argument #2 ($dateFilter) must be of type string, array given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Modules\\Client\\Services\\SecurityLogService.php on line 92","code":0}  
[2025-07-23 03:18:24] local.ERROR: {"query":{"type":["REGISTER"],"date":[["today"]],"email":"<EMAIL>"},"parameter":{"type":["REGISTER"],"date":[["today"]],"email":"<EMAIL>"},"error":"TypeError","message":"App\\Modules\\Client\\Services\\SecurityLogService::applyDateFilter(): Argument #2 ($dateFilter) must be of type string, array given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Modules\\Client\\Services\\SecurityLogService.php on line 92","code":0}  
[2025-07-23 03:21:50] local.ERROR: {"query":{"date":[["today"]],"email":"<EMAIL>"},"parameter":{"date":[["today"]],"email":"<EMAIL>"},"error":"TypeError","message":"App\\Modules\\Client\\Services\\SecurityLogService::applyDateFilter(): Argument #2 ($dateFilter) must be of type string, array given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Modules\\Client\\Services\\SecurityLogService.php on line 92","code":0}  
[2025-07-23 03:29:03] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Validation\\ValidationException","message":"The id field is required.","code":0}  
[2025-07-24 01:17:06] local.INFO: user login from 127.0.0.1  
[2025-07-24 01:18:20] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "extensions" of relation "domains" does not exist
LINE 1: update "domains" set "extensions" = $1, "updated_at" = $2 wh...
                             ^ (Connection: client, SQL: update "domains" set "extensions" = ["redemptionPeriod"], "updated_at" = 2025-07-24 01:18:20 where "id" = 106)  
[2025-07-24 01:18:20] local.INFO: number of attempts: 1  
[2025-07-24 01:18:20] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"extensions\" of relation \"domains\" does not exist
LINE 1: update \"domains\" set \"extensions\" = $1, \"updated_at\" = $2 wh...
                             ^ (Connection: client, SQL: update \"domains\" set \"extensions\" = [\"redemptionPeriod\"], \"updated_at\" = 2025-07-24 01:18:20 where \"id\" = 106)","code":"42703"}  
[2025-07-24 01:18:23] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "extensions" of relation "domains" does not exist
LINE 1: update "domains" set "extensions" = $1, "updated_at" = $2 wh...
                             ^ (Connection: client, SQL: update "domains" set "extensions" = ["redemptionPeriod"], "updated_at" = 2025-07-24 01:18:23 where "id" = 105)  
[2025-07-24 01:18:23] local.INFO: number of attempts: 1  
[2025-07-24 01:18:23] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"extensions\" of relation \"domains\" does not exist
LINE 1: update \"domains\" set \"extensions\" = $1, \"updated_at\" = $2 wh...
                             ^ (Connection: client, SQL: update \"domains\" set \"extensions\" = [\"redemptionPeriod\"], \"updated_at\" = 2025-07-24 01:18:23 where \"id\" = 105)","code":"42703"}  
[2025-07-24 01:19:55] local.ERROR: HTTP request returned status code 400:
{"message":"coffeedosage.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"coffeedosage.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-07-24 01:19:55] local.ERROR: Undefined array key "data"  
[2025-07-24 01:19:55] local.INFO: number of attempts: 2  
[2025-07-24 01:19:55] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-07-24 01:19:55] local.ERROR: HTTP request returned status code 400:
{"message":"coffeedose.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"coffeedose.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-07-24 01:19:55] local.ERROR: Undefined array key "data"  
[2025-07-24 01:19:55] local.INFO: number of attempts: 2  
[2025-07-24 01:19:55] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-07-24 01:22:41] local.INFO: createPaymentWithDetails called with IDs: [2]  
[2025-07-24 01:22:41] local.INFO: Found domains for payment creation: 1  
[2025-07-24 01:22:41] local.INFO: Domains data: [{"id":2,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 01:17:49","domain_id":105,"name":"coffeedose.com","registered_domain_id":5,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-24 01:22:41] local.INFO: Redemption order created for user 7 and domain coffeedose.com  
[2025-07-24 01:22:48] local.INFO: createPaymentWithDetails called with IDs: [1]  
[2025-07-24 01:22:48] local.INFO: Found domains for payment creation: 1  
[2025-07-24 01:22:48] local.INFO: Domains data: [{"id":1,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 01:17:49","domain_id":106,"name":"coffeedosage.net","registered_domain_id":4,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-24 01:22:48] local.INFO: Redemption order created for user 7 and domain coffeedosage.net  
[2025-07-24 01:23:05] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-07-24 02:01:50] local.INFO: createPaymentWithDetails called with IDs: [13]  
[2025-07-24 02:01:50] local.INFO: Found domains for payment creation: 1  
[2025-07-24 02:01:50] local.INFO: Domains data: [{"id":13,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":91,"name":"biogenesis.com","registered_domain_id":19,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-24 02:01:50] local.INFO: Redemption order created for user 7 and domain biogenesis.com  
[2025-07-24 02:02:38] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-07-24 02:27:00] local.INFO: createPaymentWithDetails called with IDs: [12,11]  
[2025-07-24 02:27:00] local.INFO: Found domains for payment creation: 2  
[2025-07-24 02:27:00] local.INFO: Domains data: [{"id":11,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":93,"name":"redmonday.net","registered_domain_id":17,"user_email":"<EMAIL>","user_id":7},{"id":12,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":92,"name":"biogenesis.net","registered_domain_id":18,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-24 02:27:00] local.INFO: Redemption order created for user 7 and domain redmonday.net  
[2025-07-24 02:27:00] local.INFO: Redemption order created for user 7 and domain biogenesis.net  
[2025-07-24 02:27:06] local.INFO: createPaymentWithDetails called with IDs: [10]  
[2025-07-24 02:27:06] local.INFO: Found domains for payment creation: 1  
[2025-07-24 02:27:06] local.INFO: Domains data: [{"id":10,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":94,"name":"redmonday.com","registered_domain_id":16,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-24 02:27:06] local.INFO: Redemption order created for user 7 and domain redmonday.com  
[2025-07-24 03:10:27] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key 0","code":0}  
[2025-07-24 03:25:15] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-24 03:25:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-24 03:28:54] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-24 03:29:26] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-24 03:29:57] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-24 03:30:23] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 00:55:29] local.INFO: user login from 127.0.0.1  
[2025-07-28 01:18:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 01:20:06] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 01:21:40] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 01:21:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 01:22:07] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 01:22:12] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 02:00:46] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 03:57:31] local.INFO: Permissions Sync  
[2025-07-28 05:53:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\Console\\Exception\\CommandNotFoundException","message":"Command \"migratye\" is not defined.

Did you mean one of these?
    migrate
    migrate:fresh
    migrate:install
    migrate:refresh
    migrate:reset
    migrate:rollback
    migrate:status","code":0}  
[2025-07-28 06:10:47] local.INFO: Permissions Sync  
[2025-07-28 06:27:05] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:27:32] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:32:46] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:42:15] local.INFO: createPaymentWithDetails called with IDs: [9]  
[2025-07-28 06:42:15] local.INFO: Found domains for payment creation: 1  
[2025-07-28 06:42:15] local.INFO: Domains data: [{"id":9,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":95,"name":"cdforfree.com","registered_domain_id":15,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-28 06:42:18] local.INFO: Redemption order created for user 7 and domain cdforfree.com  
[2025-07-28 06:45:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:46:40] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:46:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:48:15] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:49:18] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:51:00] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:52:38] local.INFO: createPaymentWithDetails called with IDs: [9]  
[2025-07-28 06:52:38] local.INFO: Found domains for payment creation: 1  
[2025-07-28 06:52:38] local.INFO: Domains data: [{"id":9,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":95,"name":"cdforfree.com","registered_domain_id":15,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-28 06:52:38] local.INFO: Redemption order created for user 7 and domain cdforfree.com  
[2025-07-28 06:58:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:58:15] local.INFO: createPaymentWithDetails called with IDs: [8]  
[2025-07-28 06:58:16] local.INFO: Found domains for payment creation: 1  
[2025-07-28 06:58:16] local.INFO: Domains data: [{"id":8,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":96,"name":"cdforfree.net","registered_domain_id":14,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-28 06:58:16] local.INFO: Redemption order created for user 7 and domain cdforfree.net  
[2025-07-28 06:58:16] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:58:26] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-28 06:58:33] local.INFO: createPaymentWithDetails called with IDs: [7]  
[2025-07-28 06:58:33] local.INFO: Found domains for payment creation: 1  
[2025-07-28 06:58:33] local.INFO: Domains data: [{"id":7,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-24 02:00:29","domain_id":97,"name":"pwdforyou.net","registered_domain_id":13,"user_email":"<EMAIL>","user_id":7}]  
[2025-07-28 06:58:33] local.INFO: Redemption order created for user 7 and domain pwdforyou.net  
[2025-07-28 07:38:00] local.INFO: Redemption order created for user 7 and domain midnightvortex.net  
[2025-07-29 01:05:47] local.INFO: user login from 127.0.0.1  
[2025-07-29 01:08:42] local.INFO: Redemption order created for user 7 and domain cdforfree.net  
[2025-07-29 01:08:42] local.INFO: Redemption order created for user 7 and domain cdforfree.com  
[2025-07-29 01:19:52] local.INFO: Redemption order created for user 7 and domain midnightvortex.net  
[2025-07-29 01:19:52] local.INFO: Redemption order created for user 7 and domain pwdforyou.net  
[2025-07-29 01:21:38] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-29 01:39:37] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-29 02:00:32] local.ERROR: {"query":[],"parameter":{"domain_ids":[5]},"error":"TypeError","message":"Illuminate\\Mail\\Mailables\\Address::__construct(): Argument #1 ($address) must be of type string, null given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Mail\\ClientHoldMail.php on line 32","code":0}  
[2025-07-29 02:00:53] local.INFO: Client hold status update queued for domain underscore.org. Action: client_hold  
[2025-07-29 02:03:50] local.INFO: Client hold status update queued for domain underscore.com. Action: client_hold  
[2025-07-29 02:03:50] local.INFO: Client hold status update queued for domain redmonday.com. Action: client_hold  
[2025-07-29 02:04:33] local.ERROR: Guest User cURL error 6: Could not resolve host: REGISTRY_API_URL (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for REGISTRY_API_URL/verisign/v3_1/domain/info  
[2025-07-29 02:04:33] local.ERROR: Failed to process domain underscore.com: Error Unknown  
[2025-07-29 02:04:34] local.ERROR: Guest User cURL error 6: Could not resolve host: REGISTRY_API_URL (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for REGISTRY_API_URL/verisign/v3_1/domain/info  
[2025-07-29 02:04:34] local.ERROR: Failed to process domain redmonday.com: Error Unknown  
[2025-07-29 02:04:35] local.ERROR: Guest User cURL error 6: Could not resolve host: REGISTRY_API_URL (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for REGISTRY_API_URL/pir/v3_1/domain/info  
[2025-07-29 02:04:35] local.ERROR: Failed to process domain underscore.org: Error Unknown  
[2025-07-29 02:24:41] local.INFO: Client hold status update queued for domain biogenesis.com. Action: client_hold  
[2025-07-29 02:24:49] local.ERROR: Guest User cURL error 6: Could not resolve host: REGISTRY_API_URL (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for REGISTRY_API_URL/verisign/v3_1/domain/info  
[2025-07-29 02:24:49] local.ERROR: Failed to process domain biogenesis.com: Error Unknown  
[2025-07-29 02:30:11] local.INFO: Client hold status update queued for domain underscore.org. Action: client_hold  
[2025-07-29 02:30:11] local.INFO: Client hold status update queued for domain underscore.net. Action: client_hold  
[2025-07-29 02:30:28] local.INFO: Successfully updated domain underscore.net  
[2025-07-29 02:30:33] local.INFO: Successfully updated domain underscore.org  
[2025-07-29 02:31:18] local.INFO: Client hold status update queued for domain underscore.net. Action: client_unhold  
[2025-07-29 02:31:18] local.INFO: Client hold status update queued for domain underscore.org. Action: client_unhold  
[2025-07-29 02:31:33] local.INFO: Successfully updated domain underscore.net  
[2025-07-29 02:31:37] local.INFO: Successfully updated domain underscore.org  
[2025-07-29 02:51:09] local.INFO: change status of client id 1 to ,2025-07-29 02:51:09,2025-07-29 02:51:09  
[2025-07-29 02:51:09] local.INFO: deleted client1  
[2025-07-29 02:59:04] local.INFO: log out  
[2025-07-29 02:59:20] local.INFO: user login from 127.0.0.1  
[2025-07-29 03:00:38] local.INFO: log out  
[2025-07-29 03:00:49] local.INFO: user login from 127.0.0.1  
[2025-07-29 03:08:55] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-29 03:10:10] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-29 03:17:08] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-29 03:17:17] local.INFO: change status of client id 1 to ,2025-07-29 03:17:17,2025-07-29 03:17:17  
[2025-07-29 03:17:17] local.INFO: deleted client1  
[2025-07-29 03:19:10] local.INFO: change status of client id 1 to ,2025-07-29 03:19:10,2025-07-29 03:19:10  
[2025-07-29 03:19:10] local.INFO: deleted client1  
[2025-07-29 03:32:39] local.ERROR: {"query":[],"parameter":{"email":"<EMAIL>","disable_verification":true,"disable_deposit":true,"default_balance":true,"balance":50},"error":"Illuminate\\Validation\\ValidationException","message":"The system credit is not sufficient to send an invitation.","code":0}  
[2025-07-30 01:36:04] local.INFO: user login from 127.0.0.1  
[2025-07-30 01:59:14] local.ERROR: {"query":[],"parameter":[],"error":"Error","message":"Object of class stdClass could not be converted to string","code":0}  
[2025-07-30 02:00:14] local.INFO: Domains:{"id":4,"created_at":"2025-07-24 01:15:02","updated_at":"2025-07-30 01:58:50","domain_id":101,"name":"cosmicnova.net","registered_domain_id":9,"user_email":"<EMAIL>","user_id":7,"first_name":"Julius","last_name":"Coloma"}  
[2025-07-30 02:10:10] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-30 02:10:23] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-30 02:12:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-30 02:12:15] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-07-30 03:04:45] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-07-30 06:00:40] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-07-30 06:00:58] local.INFO: user login from 127.0.0.1  
[2025-07-30 06:25:19] local.INFO: Redemption order created for user 7 and domain biogenesis.net  
[2025-07-30 06:25:58] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-07-30 06:37:01] local.INFO: Redemption order created for user 7 and domain biogenesis.net  
[2025-07-30 06:37:45] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-07-30 06:54:40] local.INFO: Redemption order created for user 7 and domain biogenesis.net  
[2025-07-30 06:55:00] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-07-31 05:08:35] local.INFO: user login from 127.0.0.1  
[2025-08-01 02:17:38] local.INFO: user login from 127.0.0.1  
[2025-08-01 02:26:32] local.ERROR: {"query":[],"parameter":{"selected_users":[],"selection_mode":"all","title":"Testing schedule notification","message":"test test","link_name":"render","redirect_url":"domain","type":"Important","schedule_type":"one-time","time":"03:26","start_date":"2025-08-02T16:00:00.000Z","weekday":[],"day_of_month":null,"month":null,"min_registration_period":null,"max_registration_period":null,"expiration":null,"preview_emails":["<EMAIL>","c@c.c","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]},"error":"Illuminate\\Validation\\ValidationException","message":"The redirect url field must start with one of the following: \/.","code":0}  
[2025-08-01 09:26:17] local.INFO: user login from 127.0.0.1  
[2025-08-01 09:35:17] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:37:19] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:37:30] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:37:47] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:38:13] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:40:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:41:01] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:41:56] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-01 09:44:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-04 01:28:17] local.INFO: user login from 127.0.0.1  
[2025-08-04 01:33:48] local.INFO: Redemption order created for user 7 and domain biogenesis.net  
[2025-08-04 01:42:35] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain-redemption\/create-payment. Supported methods: POST.","code":0}  
[2025-08-04 01:43:25] local.INFO: Redemption order created for user 7 and domain redmonday.net  
[2025-08-04 01:43:53] local.INFO: Redemption order created for user 7 and domain cdforfree.com  
[2025-08-04 01:44:43] local.INFO: Redemption order created for user 7 and domain cdforfree.net  
[2025-08-04 05:03:21] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-08-04 05:03:22] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-04 05:04:31] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-08-04 05:04:38] local.INFO: user login from 127.0.0.1  
[2025-08-04 05:11:29] local.ERROR: {"query":[],"parameter":{"name":null,"permissions":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,110,111,112,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133]},"error":"Illuminate\\Validation\\ValidationException","message":"The name field is required.","code":0}  
[2025-08-04 05:11:36] local.INFO: category Test Cat with ID Number 1 created  
[2025-08-04 05:17:41] local.INFO: category Cat1 with ID Number 2 created  
[2025-08-04 05:26:39] local.INFO: admin John |  <EMAIL> with ID Number 3 created  
[2025-08-04 05:26:40] local.ERROR: {"query":[],"parameter":{"name":"John","email":"<EMAIL>","roleId":"2","permissions":[1,2,3,4,131,71,72,70,69,124,125,126,129,127,128,5,8,6,14,9,10,12,11,23,24,25,15,16,20,19,27,18,17,26,22,21,13,7,73,122,120,123,121,119,118,66,65,132,133,111,63,64,117,116,115,114,110,112,68,67,61,62,31,32,33,37,38,39,34,36,35,40,42,43,47,46,44,45,41,74,75,48,49,50,52,51,53,76,78,77,60,58,59,56,57,54,55,28,29,30,130]},"error":"Illuminate\\Validation\\ValidationException","message":"The email has already been taken.","code":0}  
[2025-08-04 05:26:47] local.INFO: admin John |  <EMAIL> with ID Number 4 created  
[2025-08-04 05:29:28] local.INFO: category Cat2 with ID Number 3 created  
[2025-08-04 05:29:57] local.INFO: category Cat2 with ID Number 3 updated  
[2025-08-04 05:30:18] local.INFO: role Test1 with ID Number 2 deleted  
[2025-08-04 05:31:35] local.INFO: category Cat1 with ID Number 2 deleted  
[2025-08-04 05:36:03] local.INFO: admin testtest |  <EMAIL> with ID Number 5 created  
[2025-08-04 08:26:54] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-05 03:44:55] local.INFO: user login from 127.0.0.1  
[2025-08-05 05:06:07] local.INFO: Redemption order created for user 7 and domain biogenesis.net  
[2025-08-05 05:07:07] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-06 01:06:28] local.INFO: user login from 127.0.0.1  
[2025-08-06 01:31:38] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-06 01:37:24] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-06 02:05:20] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-06 02:10:44] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-06 02:11:30] local.INFO: log out  
[2025-08-06 02:11:35] local.INFO: user login from 127.0.0.1  
[2025-08-06 02:22:08] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-06 03:47:43] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 03:53:45] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 03:53:56] local.ERROR: {"query":[],"parameter":{"domainName":"restaurantzxczxc.org","userEmail":"<EMAIL>","domainId":111,"createdDate":"2025-08-06 00:58:58","userID":7,"reason":"test estsetse tset se"},"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"You do not have permissions to access this page","code":0}  
[2025-08-06 03:54:12] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 03:54:25] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 03:54:38] local.ERROR: {"query":[],"parameter":{"domainName":"restaurantzxczxc.org","userEmail":"<EMAIL>","domainId":111,"createdDate":"2025-08-06 00:58:58","userID":7,"reason":"tes tsetset set se"},"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"You do not have permissions to access this page","code":0}  
[2025-08-06 04:57:31] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 04:57:43] local.ERROR: {"query":[],"parameter":{"domainName":"storingtiles.org","userEmail":"<EMAIL>","domainId":115,"createdDate":"2025-08-06 01:29:28","userID":7,"reason":"test set set set sets"},"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"You do not have permissions to access this page","code":0}  
[2025-08-06 04:58:09] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 04:58:24] local.ERROR: {"query":[],"parameter":{"domainName":"storingtiles.com","userEmail":"<EMAIL>","domainId":113,"createdDate":"2025-08-06 01:29:25","userID":7,"reason":"testset setes tset es"},"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"You do not have permissions to access this page","code":0}  
[2025-08-06 05:03:09] local.INFO: user login from 127.0.0.1  
[2025-08-06 05:03:40] local.ERROR: {"query":[],"parameter":{"domainName":"storingtiles.net","userEmail":"<EMAIL>","domainId":114,"createdDate":"2025-08-06 01:29:23","userID":7,"reason":"test setset setset"},"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"You do not have permissions to access this page","code":0}  
[2025-08-06 05:12:22] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 05:12:40] local.ERROR: {"query":[],"parameter":{"domainName":"restaurantzxczxc.org","userEmail":"<EMAIL>","domainId":111,"createdDate":"2025-08-06 00:58:58","userID":7,"reason":"das dasd as das dasd as"},"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"You do not have permissions to access this page","code":0}  
[2025-08-06 05:13:21] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 05:13:32] local.ERROR: {"query":[],"parameter":{"domainName":"hanliy.net","userEmail":"<EMAIL>","domainId":109,"createdDate":"2025-07-30 05:37:40","userID":7,"reason":"asd asd asd asd"},"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"You do not have permissions to access this page","code":0}  
[2025-08-06 05:14:16] local.INFO: Permissions Sync  
[2025-08-06 05:14:21] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 05:14:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 05:14:38] local.ERROR: {"query":[],"parameter":{"domainName":"hanliy.net","userEmail":"<EMAIL>","domainId":109,"createdDate":"2025-07-30 05:37:40","userID":7,"reason":"asd asdasd asd asda"},"error":"ErrorException","message":"Undefined array key \"support_note\"","code":0}  
[2025-08-06 05:22:47] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 05:33:16] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-06 05:42:09] local.ERROR: {"query":[],"parameter":{"domainName":"hanliy.com","userEmail":"<EMAIL>","domainId":107,"createdDate":"2025-07-30 05:37:42","userID":7,"reason":"asds asd zxc asd asdsa sda"},"error":"Illuminate\\Database\\QueryException","message":"Array to string conversion (Connection: client, SQL: insert into \"domain_transaction_histories\" (\"domain_id\", \"type\", \"user_id\", \"status\", \"message\", \"payload\") values (107, DOMAIN_DELETED, 7, success, Domain  \"hanliy.com\" deleted by admin 1 (a@a.a), ?))","code":0}  
[2025-08-06 05:43:46] local.ERROR: {"query":[],"parameter":{"domainName":"restaurantzxczxc.net","userEmail":"<EMAIL>","domainId":112,"createdDate":"2025-08-06 00:58:52","userID":7,"reason":"asd adzxca sdas dad zxc"},"error":"ErrorException","message":"Undefined array key \"support_note\"","code":0}  
[2025-08-06 07:54:25] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-08 01:10:29] local.INFO: user login from 127.0.0.1  
[2025-08-08 06:57:07] local.INFO: user login from 127.0.0.1  
[2025-08-08 07:00:45] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-08 07:03:33] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-08 07:03:39] local.INFO: update fee REDEMPTION to 50  
[2025-08-08 07:03:42] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-08 07:04:33] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-08 07:06:09] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-08 07:16:46] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-08 08:09:00] local.INFO: update org extension fee of REDEMPTION to 10  
[2025-08-08 08:09:17] local.INFO: update net extension fee of REDEMPTION to 60  
[2025-08-08 09:44:37] local.ERROR: {"query":{"extension":"com"},"parameter":{"extension":"com"},"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-08-12 00:46:34] local.INFO: user login from 127.0.0.1  
[2025-08-12 09:44:24] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-08-12 09:44:38] local.INFO: user login from 127.0.0.1  
[2025-08-13 01:03:27] local.INFO: user login from 127.0.0.1  
