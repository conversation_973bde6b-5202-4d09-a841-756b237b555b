# Domain Redemption Scheduler

## Overview

The Domain Redemption Scheduler is an automated system that monitors domains in redemption period and automatically triggers the deletion process when domains have been in redemption for a specified number of days (default: 31 days).

## Components

### 1. Console Command
- **File**: `app/Console/Commands/Domain/DomainRedemptionScheduler.php`
- **Command**: `php artisan app:domain-redemption-scheduler`
- **Purpose**: Main scheduler command that runs the redemption period check and deletion process

### 2. Query Service
- **File**: `app/Modules/PendingDelete/Services/RedemptionPeriodQueryService.php`
- **Purpose**: Handles database queries for domains in redemption period and related operations

### 3. Configuration
- **File**: `config/domain.php`
- **Settings**:
  - `domain.redemption.default_days`: Default number of days (31)
  - `domain.redemption.scheduler_enabled`: Enable/disable scheduler (true)

## How It Works

1. **Daily Execution**: The scheduler runs daily via <PERSON><PERSON>'s task scheduler
2. **Domain Query**: Finds domains with status `REDEMPTION` that have been deleted for 31+ days
3. **Pending Deletion**: Creates records in `pending_domain_deletions` table
4. **Deletion Process**: Triggers `PendingDeleteService::delete()` to process the domains
5. **Logging**: Comprehensive logging of all operations

## Configuration

### Environment Variables
```env
# Set custom redemption period (default: 31 days)
DOMAIN_REDEMPTION_DAYS=31

# Enable/disable the scheduler (default: true)
DOMAIN_REDEMPTION_SCHEDULER_ENABLED=true
```

### Manual Execution
```bash
# Run with default settings (31 days)
php artisan app:domain-redemption-scheduler

# Run with custom redemption period
php artisan app:domain-redemption-scheduler --redemption-days=45

# Check what domains would be affected (dry run - not implemented yet)
php artisan app:domain-redemption-scheduler --dry-run
```

## Database Requirements

The scheduler expects the following database structure:
- `domains` table with `status` and `deleted_at` columns
- `registered_domains` table linking domains to users
- `pending_domain_deletions` table for tracking deletion requests
- `user_contacts` and `users` tables for user information

## Logging

All operations are logged using the `AuthLogger` service:
- Start/completion of scheduler runs
- Number of domains found and processed
- Errors and exceptions
- Individual domain processing details

## Integration with Existing System

The scheduler integrates seamlessly with the existing domain deletion workflow:
1. Uses the same `PendingDeleteService` for actual deletion
2. Follows the same database patterns as manual deletions
3. Triggers the same EPP deletion jobs and notifications
4. Maintains audit trail through `AdminActionEvent`

## Monitoring

Monitor the scheduler through:
- Laravel logs (`storage/logs/laravel.log`)
- AuthLogger entries
- Database records in `pending_domain_deletions`
- Queue job status for EPP deletions

## Troubleshooting

### Common Issues
1. **No domains processed**: Check if domains have correct status and deleted_at dates
2. **Scheduler not running**: Verify cron job is set up for Laravel scheduler
3. **Configuration issues**: Check environment variables and config cache

### Debug Commands
```bash
# Check scheduler status
php artisan schedule:list

# Run scheduler manually for testing
php artisan app:domain-redemption-scheduler --redemption-days=1

# Clear config cache after changes
php artisan config:clear
```
