<?php

namespace App\Modules\PendingDelete\Services;

use App\Util\Constant\DomainStatus;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class RedemptionPeriodQueryService
{
    public static function instance(): self
    {
        return new self();
    }

    /**
     * Get domains that have been in redemption period for the specified number of days
     * This finds domains that are already in pending_domain_deletions but haven't been processed yet
     * Uses the domain expiry date to calculate redemption period
     *
     * @param int $redemptionDays Number of days since expiry
     * @return Collection
     */
    public function getDomainsInRedemptionPeriod(int $redemptionDays): Collection
    {
        $cutoffTimestamp = Carbon::now()->subDays($redemptionDays)->getTimestampMs();

        $query = DB::client()->table('pending_domain_deletions')
            ->join('registered_domains', 'registered_domains.id', '=', 'pending_domain_deletions.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->where('domains.status', '=', DomainStatus::REDEMPTION)
            ->where('domains.expiry', '<=', $cutoffTimestamp) // Use expiry timestamp
            ->whereNotNull('domains.expiry')
            ->whereNull('pending_domain_deletions.deleted_at') // Not processed yet
            ->select(
                'pending_domain_deletions.id',
                'domains.name',
                'domains.expiry',
                'domains.status as domain_status',
                'registered_domains.id as registered_domain_id',
                'users.id as user_id',
                'users.email as user_email'
            );

        // Debug: Log the SQL query
        app(\App\Modules\CustomLogger\Services\AuthLogger::class)->info("RedemptionPeriodQuery SQL: " . $query->toSql());
        app(\App\Modules\CustomLogger\Services\AuthLogger::class)->info("RedemptionPeriodQuery Bindings: " . json_encode($query->getBindings()));

        return $query->get();
    }

    /**
     * Debug method to get all domains in redemption status
     */
    public function getAllRedemptionDomains(): Collection
    {
        return DB::client()->table('domains')
            ->join('registered_domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->leftJoin('pending_domain_deletions', 'pending_domain_deletions.registered_domain_id', '=', 'registered_domains.id')
            ->where('domains.status', '=', DomainStatus::REDEMPTION)
            ->select(
                'domains.id',
                'domains.name',
                'domains.status',
                'domains.deleted_at',
                'domains.expiry',
                'registered_domains.id as registered_domain_id',
                'pending_domain_deletions.id as pending_deletion_id',
                'pending_domain_deletions.deleted_at as pending_deleted_at'
            )
            ->get();
    }

    /**
     * Create pending deletion records for the expired domains
     *
     * @param Collection $domains
     * @return void
     */
    public function createPendingDeletionRecords(Collection $domains): void
    {
        $now = Carbon::now();
        
        $insertData = $domains->map(function ($domain) use ($now) {
            return [
                'registered_domain_id' => $domain->registered_domain_id,
                'deleted_by' => 'system_scheduler',
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => null // Will be set by PendingDeleteService
            ];
        })->toArray();
        
        if (!empty($insertData)) {
            DB::client()->table('pending_domain_deletions')->insert($insertData);
        }
    }

    /**
     * Get pending deletion IDs for the given domain IDs
     *
     * @param array $domainIds
     * @return array
     */
    public function getPendingDeletionIds(array $domainIds): array
    {
        return DB::client()->table('pending_domain_deletions')
            ->join('registered_domains', 'registered_domains.id', '=', 'pending_domain_deletions.registered_domain_id')
            ->whereIn('registered_domains.domain_id', $domainIds)
            ->whereNull('pending_domain_deletions.deleted_at')
            ->pluck('pending_domain_deletions.id')
            ->toArray();
    }

    /**
     * Get count of domains in redemption period for the specified number of days
     *
     * @param int $redemptionDays
     * @return int
     */
    public function getRedemptionPeriodDomainsCount(int $redemptionDays): int
    {
        $cutoffDate = Carbon::now()->subDays($redemptionDays);
        
        return DB::client()->table('domains')
            ->join('registered_domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.status', '=', DomainStatus::REDEMPTION)
            ->where('domains.deleted_at', '<=', $cutoffDate)
            ->whereNotNull('domains.deleted_at')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('pending_domain_deletions')
                    ->whereColumn('pending_domain_deletions.registered_domain_id', 'registered_domains.id');
            })
            ->count();
    }

    /**
     * Get domains with their redemption period days
     *
     * @param int $minRedemptionDays Minimum days in redemption period
     * @return Collection
     */
    public function getDomainsWithRedemptionDays(int $minRedemptionDays = 0): Collection
    {
        return DB::client()->table('domains')
            ->join('registered_domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->where('domains.status', '=', DomainStatus::REDEMPTION)
            ->whereNotNull('domains.deleted_at')
            ->select(
                'domains.id',
                'domains.name',
                'domains.deleted_at',
                'registered_domains.id as registered_domain_id',
                'users.id as user_id',
                'users.email as user_email',
                DB::raw('DATEDIFF(NOW(), domains.deleted_at) as redemption_days')
            )
            ->havingRaw('redemption_days >= ?', [$minRedemptionDays])
            ->orderBy('domains.deleted_at', 'asc')
            ->get();
    }
}
