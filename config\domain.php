<?php

$base = '/domain';

return [
    'v3_single_pir' => '/pir/v3',
    'v3_single_verisign' => '/verisign/v3',
    'v3_multiple_pir' => '/pir/v3_1',
    'v3_multiple_verisign' => '/verisign/v3_1',
    'base' => $base,
    'info' => $base . '/info',
    'database_info' => $base . '/database-info',
    'epp_delete' => $base . '/epp-delete',
    'database_delete' => $base . '/database-delete',
    'database_sync_expiry' => $base . '/database-sync-expiry',
    'restore_accepted' => $base . '/restore-accepted',
    'status' => [
        'ok' => 'OK',
        'error' => 'error',
        'not_found' => 'NOT_FOUND',
        'internal_server_error' => 'INTERNAL_SERVER_ERROR',
    ],
    'redemption' => [
        'default_days' => env('DOMAIN_REDEMPTION_DAYS', 31),
        'scheduler_enabled' => env('DOMAIN_REDEMPTION_SCHEDULER_ENABLED', true),
    ],
];
