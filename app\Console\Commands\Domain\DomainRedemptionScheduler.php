<?php

namespace App\Console\Commands\Domain;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PendingDelete\Services\PendingDeleteService;
use App\Modules\PendingDelete\Services\RedemptionPeriodQueryService;
use Exception;
use Illuminate\Console\Command;

class DomainRedemptionScheduler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:domain-redemption-scheduler {--redemption-days= : Number of days in redemption period before deletion (defaults to config value)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically delete domains that have been in redemption period for the specified number of days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Check if scheduler is enabled
            if (!config('domain.redemption.scheduler_enabled', true)) {
                app(AuthLogger::class)->info("DomainRedemptionScheduler: Scheduler is disabled via configuration");
                $this->info("Domain redemption scheduler is disabled via configuration");
                return;
            }

            // Get redemption days from option or config
            $redemptionDays = $this->option('redemption-days')
                ? (int) $this->option('redemption-days')
                : config('domain.redemption.default_days', 31);

            $queryService = RedemptionPeriodQueryService::instance();

            app(AuthLogger::class)->info("DomainRedemptionScheduler: Starting with redemption period of {$redemptionDays} days");

            // Debug: Get all redemption domains first
            $allRedemptionDomains = $queryService->getAllRedemptionDomains();
            app(AuthLogger::class)->info("DomainRedemptionScheduler: Found " . $allRedemptionDomains->count() . " total domains in redemption status");
            $this->info("Found " . $allRedemptionDomains->count() . " total domains in redemption status");

            foreach ($allRedemptionDomains as $domain) {
                $daysExpired = $domain->expiry ? \Carbon\Carbon::createFromTimestampMs($domain->expiry)->diffInDays(\Carbon\Carbon::now()) : 'N/A';
                $expiryDate = $domain->expiry ? \Carbon\Carbon::createFromTimestampMs($domain->expiry)->toDateString() : 'N/A';
                $this->info("Domain: {$domain->name}, Status: {$domain->status}, Days expired: {$daysExpired}, Expiry: {$expiryDate}, Pending ID: {$domain->pending_deletion_id}");
            }

            $expiredDomains = $queryService->getDomainsInRedemptionPeriod($redemptionDays);

            if ($expiredDomains->isEmpty()) {
                app(AuthLogger::class)->info("DomainRedemptionScheduler: No domains found for deletion");
                $this->info("No domains found for deletion");
                return;
            }

            $pendingDeletionIds = $expiredDomains->pluck('id')->toArray();
            $domainNames = $expiredDomains->pluck('name')->toArray();

            app(AuthLogger::class)->info("DomainRedemptionScheduler: Found " . count($pendingDeletionIds) . " domains for deletion: " . implode(', ', $domainNames));
            $this->info("Found " . count($pendingDeletionIds) . " domains for deletion: " . implode(', ', $domainNames));

            // Trigger the PendingDeleteService delete function
            PendingDeleteService::instance()->delete($pendingDeletionIds);

            app(AuthLogger::class)->info("DomainRedemptionScheduler: Successfully processed " . count($pendingDeletionIds) . " domains for deletion");
            $this->info("Successfully processed " . count($pendingDeletionIds) . " domains for deletion");

            app(AuthLogger::class)->info("DomainRedemptionScheduler: Completed successfully");

        } catch (Exception $e) {
            $errorMsg = 'DomainRedemptionScheduler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            $this->error($e->getMessage());
            throw new Exception($errorMsg);
        }
    }


}
