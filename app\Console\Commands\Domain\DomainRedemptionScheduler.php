<?php

namespace App\Console\Commands\Domain;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PendingDelete\Services\RedemptionPeriodQueryService;
use Exception;
use Illuminate\Console\Command;

class DomainRedemptionScheduler extends Command
{
    private $redemptionDays = 31;

    protected $signature = 'app:domain-redemption-scheduler {--redemption-days= : Number of days in redemption period before deletion (defaults to 31)}';

    protected $description = 'Automatically delete domains that have been expired for the specified number of days';

    public function handle()
    {
        try {
            if (!config('domain.redemption.scheduler_enabled', true)) {
                app(AuthLogger::class)->info("DomainRedemptionScheduler: Scheduler is disabled via configuration");
                $this->info("Domain redemption scheduler is disabled via configuration");
                return;
            }

            // Get redemption days from option or use default
            $redemptionDays = $this->option('redemption-days')
                ? (int) $this->option('redemption-days')
                : $this->redemptionDays;

            $queryService = RedemptionPeriodQueryService::instance();

            app(AuthLogger::class)->info("DomainRedemptionScheduler: Starting with {$redemptionDays} days expired threshold");

            // Process expired domains using the service
            $result = $queryService->processExpiredDomains($redemptionDays);

            if ($result['count'] === 0) {
                app(AuthLogger::class)->info("DomainRedemptionScheduler: No domains found for deletion");
                $this->info("No domains found for deletion");
                return;
            }

            app(AuthLogger::class)->info("DomainRedemptionScheduler: Successfully processed {$result['count']} domains for deletion: " . implode(', ', $result['domains']));
            $this->info("Successfully processed {$result['count']} domains for deletion: " . implode(', ', $result['domains']));

            app(AuthLogger::class)->info("DomainRedemptionScheduler: Completed successfully");

        } catch (Exception $e) {
            $errorMsg = 'DomainRedemptionScheduler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            $this->error($e->getMessage());
            throw new Exception($errorMsg);
        }
    }


}
